import api from "../lib/axiosInstance";

// Types based on the API response structure
export interface User {
  id: number;
  email: string;
  username: string;
  first_name: string;
  last_name: string;
  full_name: string;
  phone: string;
  company: number;
  company_name: string;
  department: number | null;
  department_name?: string;
  groups: string[];
  manager: number | null;
  manager_name?: string;
  auth_source: string;
  auth_source_display: string;
  is_active: boolean;
  date_joined: string;
  last_login: string | null;
  expense_limit: string | null;
  can_approve_expenses: boolean;
}

export interface UsersResponse {
  status: boolean;
  message: string;
  data: {
    count: number;
    next: string | null;
    previous: string | null;
    results: User[];
  };
}

export interface BulkUploadResponse {
  status: boolean;
  message: string;
  data?: any;
}

// API functions
export const fetchUsers = async (page = 1, pageSize = 10): Promise<UsersResponse> => {
  const response = await api.get(`/api/core/users/?page=${page}&page_size=${pageSize}`);
  return response.data;
};

export const bulkUploadUsers = async (file: File): Promise<BulkUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  
  const response = await api.post('/api/core/users/bulk_upload/', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  
  return response.data;
};
