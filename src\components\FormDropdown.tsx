import { useState } from 'react';
import { ChevronDown } from 'lucide-react';

interface DropdownProps {
  value: string;
  onChange: (value: string) => void;
  options: string[];
  label: string;
  className?: string;
  placeholder?: string;
  required?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export default function FormDropdown({
  value,
  onChange,
  options,
  label,
  className = '',
  placeholder = `Select ${label.toLowerCase()}`,
  required = false,
  size = 'md',
}: DropdownProps) {
  const [open, setOpen] = useState(false);
  const displayLabel = value || placeholder;

  const handleSelect = (opt: string) => {
    onChange(opt);
    setOpen(false);
  };

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg',
  };

  return (
    <div className={`w-full ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <div
        tabIndex={0}
        onBlur={(e) => {
          if (!e.currentTarget.contains(e.relatedTarget)) {
            setOpen(false);
          }
        }}
        className="relative"
      >
        <button
          type="button"
          onClick={() => setOpen((prev) => !prev)}
          className={`w-full text-left border border-gray-300 rounded-md text-sm text-gray-500 font-semibold bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition flex items-center justify-between ${
            sizeClasses[size]
          } ${value ? 'text-gray-800' : 'text-gray-500'}`}
        >
          <span className="text-gray-500">{displayLabel}</span>
          <ChevronDown
            className={`w-4 h-4 ml-2 transition-transform duration-200 ${open ? 'rotate-180' : ''}`}
          />
        </button>

        {open && (
          <div className="absolute z-20 mt-2 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto w-full">
            {options.map((opt) => (
              <div
                key={opt}
                onClick={() => handleSelect(opt)}
                className={`px-4 py-2 cursor-pointer text-sm hover:bg-blue-50 ${
                  value === opt ? 'bg-blue-100 font-medium' : ''
                }`}
              >
                {opt}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
