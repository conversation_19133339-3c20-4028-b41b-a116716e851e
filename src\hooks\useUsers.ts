import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { fetchUsers, bulkUploadUsers } from "../api/users";
import type { UsersResponse, BulkUploadResponse } from "../api/users";

// Query key factory
export const userKeys = {
  all: ['users'] as const,
  lists: () => [...userKeys.all, 'list'] as const,
  list: (page: number, pageSize: number) => [...userKeys.lists(), { page, pageSize }] as const,
};

// Hook to fetch users
export const useUsers = (page = 1, pageSize = 10) => {
  return useQuery<UsersResponse>({
    queryKey: userKeys.list(page, pageSize),
    queryFn: () => fetchUsers(page, pageSize),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Hook for bulk upload users
export const useBulkUploadUsers = () => {
  const queryClient = useQueryClient();
  
  return useMutation<BulkUploadResponse, Error, File>({
    mutationFn: bulkUploadUsers,
    onSuccess: () => {
      // Invalidate and refetch users data after successful upload
      queryClient.invalidateQueries({ queryKey: userKeys.all });
    },
  });
};
