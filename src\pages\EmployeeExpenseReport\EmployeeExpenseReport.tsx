import ApprovalLogs from './components/ApprovalLogs';
import CategoryBreakdownChart from './components/CategoryBreakdownChart';
import DetailedExpensetable from './components/DetailedExpensetable';
import DisputeExceptionTable from './components/DisputeExceptionTable';
import EmployeeOverview from './components/EmployeeOverview';
import ReportPageHeader from './components/ReportPageHeader';

const EmployeeExpenseReport = () => {
  return (
    <div className="p-4 sm:p-6 bg-gray-50 min-h-screen">
      <ReportPageHeader />
      <div className="bg-white p-8 rounded-lg shadow-sm">
        <div className="space-y-16">
          <EmployeeOverview />
          <DetailedExpensetable />
          <CategoryBreakdownChart />
          <DisputeExceptionTable />
          <ApprovalLogs />
        </div>
      </div>
    </div>
  );
};

export default EmployeeExpenseReport;
