import React from 'react';
import { CheckCircle } from 'lucide-react';

const approvalLogs = [
  {
    id: 1,
    title: 'Manager approved "Client Dinner"',
    details: "(<PERSON>'s manager, 1.2 days after submission)",
  },
  {
    id: 2,
    title: 'Manager approved "Travel to Conference"',
    details: "(<PERSON>'s manager, 1.5 days after submission)",
  },
  {
    id: 3,
    title: 'Manager approved "Hotel Stay"',
    details: "(<PERSON>'s manager, 1.3 days after submission)",
  },
  {
    id: 4,
    title: 'Manager approved "Conference Fees"',
    details: "(Sophia's manager, 1.1 days after submission)",
  },
  {
    id: 5,
    title: 'Manager approved "Client Lunch"',
    details: "(<PERSON>'s manager, 1.4 days after submission)",
  },
  {
    id: 6,
    title: 'Manager approved "Office Supplies"',
    details: "(<PERSON>'s manager, 1.0 days after submission)",
  },
  {
    id: 7,
    title: 'Manager approved "Team Building Event"',
    details: "(<PERSON>'s manager, 1.6 days after submission)",
  },
  {
    id: 8,
    title: 'Manager approved "Software Subscription"',
    details: "(<PERSON>'s manager, 0.8 days after submission)",
  },
  {
    id: 9,
    title: 'Manager approved "Client Meeting"',
    details: "(<PERSON>'s manager, 1.2 days after submission)",
  },
  {
    id: 10,
    title: 'Manager approved "Travel to Client Site"',
    details: "(Sophia's manager, 1.1 days after submission)",
  },
  {
    id: 11,
    title: 'Manager approved "Project Materials"',
    details: "(Sophia's manager, 0.9 days after submission)",
  },
];

const ApprovalLogs: React.FC = () => {
  return (
    <div className="bg-gray-50 rounded-lg p-6 border border-gray-100 h-full flex flex-col">
      <h3 className="text-lg font-bold text-gray-500 mb-6">Approval Log</h3>
      <div className="relative flex-grow overflow-y-auto pr-2">
        <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200 transform -translate-x-1/2"></div>
        <div className="space-y-8">
          {approvalLogs.map((log, index) => (
            <div key={log.id} className="relative flex items-start">
              <div className="relative z-10">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center border-6 border-white">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>

                {index < approvalLogs.length - 1 && (
                  <div className="absolute top-12 left-1/2 -translate-x-1/2 h-full w-0.5 bg-gray-200 z-0" />
                )}
              </div>

              <div className="ml-4 pt-1">
                <p className="text-sm font-medium text-teal-600 leading-snug">{log.title}</p>
                <p className="text-xs text-gray-500 mt-0.5">{log.details}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ApprovalLogs;
