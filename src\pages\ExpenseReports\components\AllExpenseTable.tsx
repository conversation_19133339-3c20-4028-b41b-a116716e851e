import { useNavigate } from 'react-router-dom';
import type { ExpenseData } from '../../../mockData/mockData';

interface AllExpenseTableProps {
  data: ExpenseData[];
  getStatusBadgeClass: (status: string) => string;
}

const AllExpenseTable = ({ data, getStatusBadgeClass }: AllExpenseTableProps) => {
  const navigate = useNavigate();

  const handleNameClick = (employeeId: string) => {
    navigate(`/expense-reports/${employeeId}`);
  };

  return (
    <table className="w-full">
      <thead>
        <tr className="border-b border-gray-200">
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Employee Name</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Submission Date</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Total Amount</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Approval Status</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Payment Method</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
        </tr>
      </thead>
      <tbody>
        {data.length > 0 ? (
          data.map((expense) => (
            <tr key={expense.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-4 px-4 text-sm text-gray-900">
                <button
                  onClick={() => handleNameClick(expense.id)}
                  className="text-teal-600 hover:text-teal-800 hover:underline cursor-pointer"
                >
                  {expense.employeeName}
                </button>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{expense.department}</td>
              <td className="py-4 px-4 text-sm text-gray-600">{expense.submissionDate}</td>
              <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                ${expense.totalAmount.toFixed(2)}
              </td>
              <td className="py-4 px-4">
                <span className={getStatusBadgeClass(expense.approvalStatus)}>
                  {expense.approvalStatus}
                </span>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{expense.paymentMethod}</td>
              <td className="py-4 px-4">
                <button className="text-sm text-gray-500 hover:text-gray-700">Mark Action</button>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={7} className="py-8 px-4 text-center text-gray-500">
              No expenses found for this category
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
};

export default AllExpenseTable;
