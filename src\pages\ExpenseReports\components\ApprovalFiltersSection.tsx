import { useState } from 'react';
import Dropdown from '../../../components/Dropdown';
import DateRangePicker from '../../../components/DateRangePicker';
import type { DateRange } from 'react-day-picker';

interface ApprovalFiltersSectionProps {
  onFiltersChange?: (filters: any) => void;
}

const ApprovalFiltersSection = ({ onFiltersChange }: ApprovalFiltersSectionProps) => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(undefined);
  const [filters, setFilters] = useState({
    designation: '',
    department: '',
    status: '',
    approvalStatus: '',
  });

  const designationOptions = ['Manager', 'Senior Manager', 'Director', 'VP', 'CEO'];
  const departmentOptions = ['HR', 'Finance', 'IT', 'Marketing', 'Sales'];
  const statusOptions = ['Active', 'Inactive', 'Pending'];
  const approvalStatusOptions = ['Pending', 'Approved', 'Rejected', 'In Review'];

  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);
    onFiltersChange?.({ ...newFilters, dateRange });
  };

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    onFiltersChange?.({
      ...filters,
      dateRange: range,
    });
  };

  return (
    <div className="p-4">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <div>
          <DateRangePicker
            range={dateRange}
            onChange={handleDateRangeChange}
            placeholder="Date Range"
            className="w-full"
          />
        </div>

        <div>
          <Dropdown
            value={filters.designation}
            onChange={(value) => handleFilterChange('designation', value)}
            options={designationOptions}
            placeholder="Designation"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.department}
            onChange={(value) => handleFilterChange('department', value)}
            options={departmentOptions}
            placeholder="Department"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.status}
            onChange={(value) => handleFilterChange('status', value)}
            options={statusOptions}
            placeholder="Status"
            fullWidth
          />
        </div>

        <div>
          <Dropdown
            value={filters.approvalStatus}
            onChange={(value) => handleFilterChange('approvalStatus', value)}
            options={approvalStatusOptions}
            placeholder="Approval Status"
            fullWidth
          />
        </div>
      </div>
    </div>
  );
};

export default ApprovalFiltersSection;
