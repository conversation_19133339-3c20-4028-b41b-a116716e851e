import { useNavigate } from 'react-router-dom';
import type { DisputeData } from '../../../mockData/mockData';

interface DisputesTableProps {
  data: DisputeData[];
}

const DisputesTable = ({ data }: DisputesTableProps) => {
  const navigate = useNavigate();

  const handleNameClick = (employeeId: string) => {
    navigate(`/expense-reports/${employeeId}`);
  };

  return (
    <table className="w-full">
      <thead>
        <tr className="border-b border-gray-200">
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Name</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Department</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Expense Type</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">
            Dispute Raised On
          </th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Dispute Amount</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Rejected By</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Current Status</th>
          <th className="text-left py-3 px-4 font-medium text-gray-500 text-sm">Actions</th>
        </tr>
      </thead>
      <tbody>
        {data.length > 0 ? (
          data.map((dispute) => (
            <tr key={dispute.id} className="border-b border-gray-100 hover:bg-gray-50">
              <td className="py-4 px-4 text-sm text-gray-900">
                <button
                  onClick={() => handleNameClick(dispute.id)}
                  className="text-teal-600 hover:text-teal-800 hover:underline cursor-pointer"
                >
                  {dispute.name}
                </button>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{dispute.department}</td>
              <td className="py-4 px-4 text-sm text-gray-600">
                <div className="flex items-center gap-2">
                  <span className="w-6 h-6 bg-gray-800 rounded flex items-center justify-center">
                    <img src="/categoryIcon.svg" alt="Category Icon" />
                  </span>
                  {dispute.expenseType}
                </div>
              </td>
              <td className="py-4 px-4 text-sm text-gray-600">{dispute.disputeRaisedOn}</td>
              <td className="py-4 px-4 text-sm text-gray-900 font-medium">
                ${dispute.disputeAmount.toFixed(2)}
              </td>
              <td className="py-4 px-4">
                <div className="flex items-center bg-gray-100 rounded-full px-3 py-2 w-fit">
                  <span className="text-gray-700 text-xs font-medium">{dispute.rejectedBy}</span>
                  <div className="w-px h-4 bg-teal-400 mx-2" />
                  <img src="/tableErrorIcon.svg" alt="Error Icon" className="h-4" />
                </div>
              </td>
              <td className="py-4 px-4">
                <span
                  className={`px-3 py-1 rounded-full text-xs font-medium ${
                    dispute.currentStatus === 'Approved'
                      ? 'bg-green-100 text-green-800'
                      : dispute.currentStatus === 'Pending'
                      ? 'bg-gray-100 text-gray-800'
                      : 'bg-teal-100 text-teal-800'
                  }`}
                >
                  {dispute.currentStatus}
                </span>
              </td>
              <td className="py-4 px-4">
                <button
                  onClick={() => alert('View dispute details')}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  View Details
                </button>
              </td>
            </tr>
          ))
        ) : (
          <tr>
            <td colSpan={8} className="py-8 px-4 text-center text-gray-500">
              No disputes found
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
};

export default DisputesTable;
