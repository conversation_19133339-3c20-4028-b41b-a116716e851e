
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { adminLoginSchema } from "../../schema/adminLoginSchema";
import { z } from "zod";
import { useLoginAdmin } from "../../hooks/useLoginAdmin";
import { useNavigate } from "react-router-dom";
// import { useEffect } from "react";

const Login = () => {
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<z.infer<typeof adminLoginSchema>>({
    resolver: zodResolver(adminLoginSchema),
  });

  const { mutate, isPending, isError, error } = useLoginAdmin();

  const onSubmit = (data: z.infer<typeof adminLoginSchema>) => {
    mutate(data, {
      onSuccess: () => {
        navigate("/onboarding/organization-profile"); // update this to your intended route
      },
    });
  };

  return (
    <section className="grid grid-cols-2 gap-x-20 pt-32 relative">
      <section className="h-64 flex items-center justify-center">
        <div className="max-w-xl rounded text-white">
          <div className="flex items-center mb-4">
            <div className="mr-3 w-20 h-20">
              <img src="/expenso-logo.png" alt="Expenso Logo" className="w-full h-full object-contain" />
            </div>
            <div className="w-70 h-10">
              <img src="/expenso-text.svg" alt="EXPENSO" className="w-full h-full object-contain" />
            </div>
          </div>
          <p className="text-xs font-medium uppercase tracking-[1.4em] mb-6">Admin Dashboard</p>
          <h1 className="font-semibold mb-2 text-4xl">Sign in to</h1>
          <p className="text-lg font-light my-4">Streamline your company’s expense management <br /> in one secure platform.</p>
          <p className="text-sm font-light">Sign in to get started and take control of your <br /> expenses today.</p>
        </div>
      </section>

      <section className="z-20 mx-auto bg-white rounded-2xl p-12 shadow-lg absolute right-65 top-4/10">
        <h3 className="text-xl font-normal mb-2">
          Welcome to <span className="text-[#0ee6c9] font-semibold">EXPENSO</span>
        </h3>
        <h1 className="text-6xl font-semibold mb-12">Sign in</h1>

        <form className="grid gap-8 w-110" onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-2">
            <label htmlFor="email" className="text-sm font-normal text-gray-800">
              Enter your username or email address
            </label>
            <input
              {...register("email")}
              id="email"
              type="text"
              placeholder="Username or email address"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
            />
            {errors.email && <p className="text-sm text-red-500">{errors.email.message}</p>}
          </div>

          <div className="grid gap-2">
            <label htmlFor="password" className="text-sm font-normal text-gray-700">
              Enter your Password
            </label>
            <input
              {...register("password")}
              id="password"
              type="password"
              placeholder="Password"
              className="w-full rounded-lg text-gray-500 font-medium text-md border border-gray-300 py-4 px-5 focus:border-[#0ee6c9] focus:ring-1 focus:ring-[#0ee6c9] outline-none"
            />
            {errors.password && <p className="text-sm text-red-500">{errors.password.message}</p>}
          </div>

          {/* Optional: Error message from API */}
          {isError && (
            <p className="text-sm text-red-500 -mt-4">
              {(error as any)?.response?.data?.detail || "Login failed"}
            </p>
          )}

          <button
            type="submit"
            disabled={isPending}
            className="brand-gradient mt-10 rounded-lg text-white py-3 font-semibold hover:opacity-90 transition flex items-center justify-center"
          >
            {isPending ? (
              <span className="h-5 w-5 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
            ) : (
              "Sign in"
            )}
          </button>
        </form>
      </section>
    </section>
  );
};

export default Login;
