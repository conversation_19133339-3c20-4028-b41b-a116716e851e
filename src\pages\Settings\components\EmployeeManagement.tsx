import React, { useState } from 'react';
import { User, FileText, Plus, Edit3, Download, Calendar, Users } from 'lucide-react';

interface Employee {
  id: string;
  name: string;
  joinedDate: string;
  role: string;
  department: string;
}

interface UserRole {
  id: string;
  department: string;
  role: string;
  addedOn: string;
  members: number;
}

const EmployeeManagement: React.FC = () => {
  const [employees] = useState<Employee[]>([
    {
      id: '1',
      name: '<PERSON>',
      joinedDate: '24/04/2025',
      role: 'Manager',
      department: 'Manager',
    },
    {
      id: '2',
      name: '<PERSON>',
      joinedDate: '24/04/2025',
      role: 'Finance',
      department: 'Finance',
    },
  ]);

  const [userRoles, setUserRoles] = useState<UserRole[]>([
    {
      id: '1',
      department: 'Accounts',
      role: 'Finance',
      addedOn: '24/04/2023',
      members: 3,
    },
    {
      id: '2',
      department: 'Accounts',
      role: 'Finance',
      addedOn: '24/04/2023',
      members: 3,
    },
  ]);

  const [selfRegistration, setSelfRegistration] = useState(false);
  const [showAddRoleModal, setShowAddRoleModal] = useState(false);
  const [newRole, setNewRole] = useState({
    department: '',
    role: '',
    members: 0,
  });

  const handleAddRole = () => {
    if (newRole.department && newRole.role) {
      const role: UserRole = {
        id: Date.now().toString(),
        department: newRole.department,
        role: newRole.role,
        addedOn: new Date().toLocaleDateString('en-GB'),
        members: newRole.members,
      };
      setUserRoles([...userRoles, role]);
      setNewRole({ department: '', role: '', members: 0 });
      setShowAddRoleModal(false);
    }
  };

  const handleDeleteRole = (id: string) => {
    setUserRoles(userRoles.filter((role) => role.id !== id));
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6 lg:p-8">
        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-semibold text-gray-900 mb-2">
                You have added 25 employees to your organization.
              </h1>
              <div className="flex items-center gap-3 text-sm text-gray-600">
                <div className="flex items-center gap-2 bg-teal-50 px-3 py-2 rounded-lg">
                  <FileText className="w-4 h-4 text-teal-600" />
                  <span className="font-medium text-teal-700">Employee List 2025</span>
                </div>
              </div>
            </div>
            <button className="bg-gray-800 hover:bg-gray-900 text-white px-6 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 self-start">
              <Download className="w-4 h-4" />
              Review File
            </button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Approver Summary</h2>
          <div className="space-y-4">
            {employees.map((employee) => (
              <div
                key={employee.id}
                className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 bg-gray-50 rounded-lg gap-3"
              >
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-teal-600 rounded-full flex items-center justify-center">
                    <User className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{employee.name}</div>
                    <div className="text-sm text-gray-500 flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      Joined on {employee.joinedDate}
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <span className="bg-teal-100 text-teal-800 px-3 py-1 rounded-full text-sm font-medium">
                    {employee.role}
                  </span>
                  <button className="text-gray-500 hover:text-gray-700 px-3 py-1 border border-gray-300 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors duration-200">
                    Edit
                  </button>
                </div>
              </div>
            ))}
          {/* </div> */}
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-1">Self-Registration</h3>
              <p className="text-gray-600 text-sm">
                Enable or disable self-registration for new users.
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={selfRegistration}
                onChange={(e) => setSelfRegistration(e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-teal-600"></div>
            </label>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <h2 className="text-lg font-semibold text-gray-900">User Roles</h2>
            <button
              onClick={() => setShowAddRoleModal(true)}
              className="bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Role
            </button>
          </div>

          <div className="hidden md:grid md:grid-cols-4 gap-4 text-sm font-medium text-gray-500 border-b border-gray-200 pb-3 mb-4">
            <div>Department</div>
            <div>Role</div>
            <div>Added On</div>
            <div>No. of members</div>
          </div>

          <div className="space-y-4">
            {userRoles.map((role) => (
              <div
                key={role.id}
                className="grid grid-cols-1 md:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg"
              >
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-500 md:hidden">Department</span>
                  <span className="text-gray-900 font-medium">{role.department}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-500 md:hidden">Role</span>
                  <span className="text-gray-900">{role.role}</span>
                </div>
                <div className="flex flex-col">
                  <span className="text-sm font-medium text-gray-500 md:hidden">Added On</span>
                  <span className="text-gray-900">{role.addedOn}</span>
                </div>
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div className="flex flex-col">
                    <span className="text-sm font-medium text-gray-500 md:hidden">
                      No. of members
                    </span>
                    <span className="text-gray-900 flex items-center gap-1">
                      <Users className="w-4 h-4" />
                      {role.members}
                    </span>
                  </div>
                  <div className="flex gap-2 mt-2 md:mt-0">
                    <button className="text-gray-500 hover:text-gray-700 p-1 hover:bg-gray-200 rounded transition-colors duration-200">
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => handleDeleteRole(role.id)}
                      className="text-red-500 hover:text-red-700 p-1 hover:bg-red-50 rounded transition-colors duration-200"
                    >
                      ×
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {showAddRoleModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
            <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Role</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Department</label>
                  <input
                    type="text"
                    value={newRole.department}
                    onChange={(e) => setNewRole({ ...newRole, department: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter department name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Role</label>
                  <input
                    type="text"
                    value={newRole.role}
                    onChange={(e) => setNewRole({ ...newRole, role: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter role name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Number of Members
                  </label>
                  <input
                    type="number"
                    value={newRole.members}
                    onChange={(e) =>
                      setNewRole({
                        ...newRole,
                        members: parseInt(e.target.value) || 0,
                      })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent"
                    placeholder="Enter number of members"
                  />
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button
                  onClick={handleAddRole}
                  className="flex-1 bg-teal-600 hover:bg-teal-700 text-white py-2 px-4 rounded-md font-medium transition-colors duration-200"
                >
                  Add Role
                </button>
                <button
                  onClick={() => setShowAddRoleModal(false)}
                  className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded-md font-medium transition-colors duration-200"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EmployeeManagement;
