import React, { useState } from 'react';

interface NotificationSetting {
  id: string;
  title: string;
  description: string;
  enabled: boolean;
}

const NotificationSettings: React.FC = () => {
  const [settings, setSettings] = useState<NotificationSetting[]>([
    {
      id: 'report-submission',
      title: 'Report Submission',
      description: 'Enable or disable email notifications for report submissions.',
      enabled: false,
    },
    {
      id: 'report-approval',
      title: 'Report Approval',
      description: 'Enable or disable email notifications for report approvals.',
      enabled: true,
    },
    {
      id: 'report-rejection',
      title: 'Report Rejection',
      description: 'Enable or disable email notifications for report rejections.',
      enabled: false,
    },
    {
      id: 'report-payment',
      title: 'Report Payment',
      description: 'Enable or disable email notifications for report payments.',
      enabled: false,
    },
    {
      id: 'policy-violations',
      title: 'Policy Violations',
      description: 'Enable or disable email notifications for policy violations.',
      enabled: true,
    },
  ]);

  const toggleSetting = (id: string) => {
    setSettings((prev) =>
      prev.map((setting) =>
        setting.id === id ? { ...setting, enabled: !setting.enabled } : setting
      )
    );
  };

  const Toggle: React.FC<{ enabled: boolean; onChange: () => void }> = ({
    enabled,
    onChange,
  }) => (
    <button
  onClick={onChange}
  className={`
    relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-300 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-teal-400 focus:ring-offset-2
    ${enabled ? "bg-[#C7FEFE] shadow-md" : "bg-gray-300"}
    transform hover:scale-105 active:scale-95
  `}
  aria-pressed={enabled}
  aria-label={`Toggle ${enabled ? "off" : "on"}`}
>
  <span
    className={`
      inline-block h-4 w-4 transform rounded-full transition-all duration-300 ease-in-out
      shadow-sm
      ${enabled ? "translate-x-6 bg-[#00DCCA]" : "translate-x-1 bg-gray-500"}
    `}
  />
</button>

  );

  return (
      <div className="max-w-full mx-auto">
          <div className=" p-6 sm:p-8 lg:p-12">
            <div className=" space-y-8 sm:space-y-10">
              {settings.map((setting, index) => (
                <div
                  key={setting.id}
                  className="group"
                  style={{
                    animationDelay: `${index * 150}ms`,
                    animation: 'slideInUp 0.6s ease-out forwards',
                    opacity: '0',
                  }}
                >
                  <div className="flex flex-col  sm:flex-row sm:items-start sm:justify-between gap-4 sm:gap-6">
                    <div className=" justify-items-start flex-1 min-w-0">
                      <h3 className="text-lg sm:text-xl font-medium text-gray-700 mb-2 transition-colors duration-200 group-hover:text-gray-900">
                        {setting.title}
                      </h3>
                      <p className="text-sm sm:text-base text-gray-500 leading-relaxed transition-colors duration-200 group-hover:text-gray-600 pr-0 sm:pr-4">
                        {setting.description}
                      </p>
                    </div>
                    <div className="flex-shrink-0 self-start sm:self-center">
                      <Toggle
                        enabled={setting.enabled}
                        onChange={() => toggleSetting(setting.id)}
                      />
                    </div>
                  </div>

                  {index < settings.length - 1 && (
                    <div className="mt-8 sm:mt-10 border-b border-gray-100 transition-colors duration-300 group-hover:border-gray-200" />
                  )}
                </div>
              ))}
            {/* </div> */}
          </div>
        {/* </div> */}

        <div className="mt-6 text-center">
          <div className="inline-flex items-center space-x-2 px-4 py-2 bg-white rounded-full shadow-sm border border-gray-100">
            <div className="flex space-x-1">
              {settings.map((setting, index) => (
                <div
                  key={setting.id}
                  className={`
                    w-2 h-2 rounded-full transition-all duration-500 ease-in-out
                    ${
                      setting.enabled
                        ? 'bg-cyan-400 scale-100 opacity-100'
                        : 'bg-gray-200 scale-75 opacity-60'
                    }
                  `}
                  style={{
                    animationDelay: `${index * 100 + 1000}ms`,
                    animation: 'bounceIn 0.6s ease-out forwards',
                  }}
                />
              ))}
            </div>
            <span className="text-xs sm:text-sm text-gray-500 font-medium">
              {settings.filter((s) => s.enabled).length}/{settings.length} active
            </span>
          </div>
        </div>
      </div>

      <style>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes bounceIn {
          0% {
            opacity: 0;
            transform: scale(0.3);
          }
          50% {
            opacity: 1;
            transform: scale(1.1);
          }
          100% {
            opacity: 1;
            transform: scale(1);
          }
        }

        /* Custom scrollbar for mobile */
        @media (max-width: 640px) {
          ::-webkit-scrollbar {
            width: 3px;
          }
          ::-webkit-scrollbar-track {
            background: #f1f5f9;
          }
          ::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
          }
        }
      `}</style>
    </div>
  );
};

export default NotificationSettings;
