import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';
import Modal from '../../../components/Modal';
import { Button } from '../../../components/Button';
import { useBulkUploadUsers } from '../../../hooks/useUsers';
import * as <PERSON> from 'papaparse';

interface AddBulkUserModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface CSVRow {
  [key: string]: string;
}

const AddBulkUserModal: React.FC<AddBulkUserModalProps> = ({ isOpen, onClose }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVRow[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const { mutate: uploadUsers, isPending } = useBulkUploadUsers();

  const parseCSV = useCallback((file: File) => {
    setIsLoading(true);
    Papa.parse<CSVRow>(file, {
      complete: (results) => {
        const data = results.data.filter((row) =>
          Object.values(row).some((value) => value && value.trim() !== '')
        );
        setCsvData(data);
        setIsLoading(false);
      },
      header: true,
      skipEmptyLines: true,
      error: (error) => {
        console.error('Error parsing CSV:', error);
        setIsLoading(false);
        alert('Error parsing CSV file. Please check the format and try again.');
      },
    });
  }, []);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        const file = acceptedFiles[0];
        setSelectedFile(file);
        parseCSV(file);
      }
    },
    [parseCSV]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'text/csv': ['.csv'],
    },
    maxFiles: 1,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedFile) {
      uploadUsers(selectedFile, {
        onSuccess: (response) => {
          if (response.status) {
            alert('Users uploaded successfully!');
            onClose();
            setSelectedFile(null);
            setCsvData([]);
          } else {
            alert(`Upload failed: ${response.message}`);
          }
        },
        onError: (error) => {
          console.error('Upload error:', error);
          alert('Failed to upload users. Please try again.');
        },
      });
    }
  };

  const handleDownloadSample = () => {
    alert('Downloading sample CSV...');
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Add Bulk User"
      className="max-w-3xl"
      disableBackdropClick
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="text-gray-600">
          <p className="text-sm font-semibold">
            Import employee data from a CSV file. Ensure your file includes columns for employee
            name, email, department, and role. Download a sample CSV to see the required format.
          </p>
        </div>

        <div className="flex justify-end">
          <Button
            type="button"
            onClick={handleDownloadSample}
            className="brand-gradient text-white text-sm px-6 py-2 rounded-full flex items-center gap-2 my-3"
          >
            Download Sample CSV
          </Button>
        </div>

        <div
          {...getRootProps()}
          className={`border-2 border-dashed rounded-lg p-10 text-center cursor-pointer transition-colors ${
            isDragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'
          } ${selectedFile ? 'bg-green-50 border-green-300' : ''}`}
        >
          <input {...getInputProps()} />

          <div className="flex flex-col items-center justify-center space-y-5">
            <div>
              <p className="text-md font-bold text-gray-800">
                {selectedFile ? `Selected: ${selectedFile.name}` : 'Drag and drop a CSV file here'}
              </p>
              <p className="text-sm text-gray-600 mt-2">Or click to browse</p>
              {isLoading && <p className="text-sm text-blue-600 mt-2">Parsing CSV...</p>}
            </div>

            {!selectedFile && (
              <Button
                type="button"
                className="brand-gradient text-white text-sm px-6 py-2 rounded-full flex items-center gap-2 my-3"
              >
                Select .CSV File
              </Button>
            )}
          </div>
        </div>

        {/* CSV Preview Section */}
        {csvData.length > 0 && (
          <div className="mt-6">
            <h4 className="text-lg font-semibold text-gray-900 mb-4">
              Preview ({csvData.length} rows)
            </h4>
            <div className="max-h-64 overflow-auto border border-gray-200 rounded-lg">
              <table className="w-full text-sm">
                <thead className="bg-gray-50 sticky top-0">
                  <tr>
                    {Object.keys(csvData[0] || {}).map((header) => (
                      <th
                        key={header}
                        className="px-4 py-2 text-left font-medium text-gray-700 border-b"
                      >
                        {header}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {csvData.slice(0, 5).map((row, index) => (
                    <tr key={index} className="border-b border-gray-100">
                      {Object.values(row).map((value, cellIndex) => (
                        <td key={cellIndex} className="px-4 py-2 text-gray-600">
                          {value}
                        </td>
                      ))}
                    </tr>
                  ))}
                  {csvData.length > 5 && (
                    <tr>
                      <td
                        colSpan={Object.keys(csvData[0] || {}).length}
                        className="px-4 py-2 text-center text-gray-500 italic"
                      >
                        ... and {csvData.length - 5} more rows
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button
            type="submit"
            disabled={!selectedFile || isPending || isLoading}
            className="brand-gradient text-white px-24 py-3 rounded-md font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isPending ? 'Uploading...' : 'Submit'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

export default AddBulkUserModal;
