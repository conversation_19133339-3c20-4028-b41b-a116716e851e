import { useState } from 'react';
import { Mail } from 'lucide-react';

import Dropdown from '../../../components/Dropdown';
import Pagination from '../../../components/Pagination';
import DatePickerWithLabel from '../../../components/DatePicker';
import type { User } from '../../../api/users';

interface UserTableProps {
  title: string;
  users: User[];
}

const ITEMS_PER_PAGE = 6;

export default function UserTable({ title, users }: UserTableProps) {
  const [roleFilter, setRoleFilter] = useState('');
  const [departmentFilter, setDepartmentFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const filteredUsers = users.filter((user) => {
    const userRole = user.groups[0] || '';
    const userDepartment = user.department_name || '';
    const userStatus = user.is_active ? 'Active' : 'Inactive';

    return (
      (roleFilter === '' || userRole === roleFilter) &&
      (departmentFilter === '' || userDepartment === departmentFilter) &&
      (statusFilter === '' || userStatus === statusFilter)
    );
  });

  const paginatedUsers = filteredUsers.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE
  );

  const totalPages = Math.ceil(filteredUsers.length / ITEMS_PER_PAGE);

  return (
    <div className="bg-white rounded-lg shadow-sm mt-6">
      <div className="p-4 sm:p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-4 sm:mb-6 gap-4">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900">{title}</h3>

          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-4">
            <div className="flex items-center gap-2">
              <DatePickerWithLabel
                selected={selectedDate}
                onChange={(date) => setSelectedDate(date)}
                placeholderText="Date"
                className="text-sm text-gray-700 w-full outline-none"
                dateFormat="dd/MM/yyyy"
              />
            </div>

            <div className="flex flex-wrap items-center gap-2">
              <Dropdown
                label="Role"
                value={roleFilter}
                onChange={setRoleFilter}
                options={['Manager', 'Analyst', 'Specialist']}
              />
              <Dropdown
                label="Department"
                value={departmentFilter}
                onChange={setDepartmentFilter}
                options={['BI', 'Finance', 'HR']}
              />
              <Dropdown
                label="Status"
                value={statusFilter}
                onChange={setStatusFilter}
                options={['Active', 'Inactive']}
              />
            </div>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full min-w-[640px] mt-8">
            <thead>
              <tr className="border-b border-gray-200">
                {['Name', 'Email', 'Department', 'Role', 'Status', 'Last Login', 'Actions'].map(
                  (label) => {
                    const hiddenClass =
                      label === 'Email'
                        ? 'hidden sm:table-cell'
                        : label === 'Last Login'
                        ? 'hidden lg:table-cell'
                        : '';
                    return (
                      <th
                        key={label}
                        className={`text-left py-3 px-2 sm:px-4 font-medium text-gray-700 text-xs sm:text-sm ${hiddenClass}`}
                      >
                        {label}
                      </th>
                    );
                  }
                )}
              </tr>
            </thead>
            <tbody>
              {paginatedUsers.map((user) => (
                <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-2 sm:px-4 align-middle">
                    <div className="flex items-center gap-2 sm:gap-3">
                      <img
                        src="https://avatar.iran.liara.run/public"
                        alt="avatar"
                        className="relative inline-block h-6 w-6 !rounded-full object-cover object-center"
                      />
                      <div className="min-w-0">
                        <div className="font-medium text-gray-900 text-xs sm:text-sm truncate">
                          {user.full_name}
                        </div>
                        <div className="text-gray-600 text-xs sm:hidden truncate">{user.email}</div>
                      </div>
                    </div>
                  </td>

                  <td className="py-3 px-2 sm:px-4 text-gray-600 text-xs sm:text-sm hidden sm:table-cell align-middle">
                    <div className="flex items-center gap-2 text-sm text-gray-700">
                      <Mail className="w-4 h-4 text-teal-800" />
                      <span>{user.email}</span>
                    </div>
                  </td>

                  <td className="py-3 px-2 sm:px-4 text-gray-600 text-xs sm:text-sm align-middle">
                    {user.department_name || 'N/A'}
                  </td>

                  <td className="py-3 px-2 sm:px-4 text-gray-800 text-xs sm:text-sm align-middle">
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100">
                      {user.groups[0] || 'N/A'}
                    </span>
                  </td>

                  <td className="py-3 px-2 sm:px-4 align-middle">
                    <span
                      className={`px-2 py-1 rounded-full text-xs font-medium ${
                        user.is_active ? 'bg-teal-100 text-teal-800' : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {user.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>

                  <td className="py-3 px-2 sm:px-4 text-gray-600 text-xs font-semibold lg:table-cell align-middle">
                    {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                  </td>

                  <td className="py-3 px-2 sm:px-4 align-middle font-semibold">
                    <div className="flex items-center gap-1 sm:gap-2">
                      <button
                        className="text-teal-600 hover:text-teal-800 text-xs sm:text-sm cursor-pointer"
                        onClick={() => alert('View user')}
                      >
                        view
                      </button>
                      <span className="text-gray-300">/</span>
                      <button
                        onClick={() => alert('Edit user')}
                        className="text-teal-600 hover:text-teal-800 text-xs sm:text-sm cursor-pointer"
                      >
                        edit
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          onPageChange={(page) => setCurrentPage(page)}
        />
      </div>
    </div>
  );
}
