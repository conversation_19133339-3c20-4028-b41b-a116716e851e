// src/schema/adminRegistrationSchema.ts
import { z } from "zod";

export const adminRegistrationSchema = z.object({
  email: z.string().email("Invalid email").max(254),
  username: z.string().min(1, "Username is required").max(30),
  first_name: z.string().min(2, "First name is required").max(50),
  last_name: z.string().min(1, "Last name is required").max(50),
  phone: z.string().max(20).optional().or(z.literal('').transform(() => undefined)), // optional field
  password: z.string().min(6, "Password must be at least 6 characters"),
  confirm_password: z.string().min(6),
}).refine((data) => data.password === data.confirm_password, {
  message: "Passwords do not match",
  path: ["confirm_password"],
});


